import { Injectable } from '@nestjs/common';
import { BasePaymentStrategy } from './base-payment.strategy';
import { PaymentContext, PaymentResult } from './payment-strategy.interface';
import { PaymentMethodEnum } from '../payment-method.enum';
import { PaymentRepository } from '../infrastructure/persistence/payment.repository';
import { BookingsService } from '../../bookings/bookings.service';
import { LoggerService } from '../../infrastructure/logger/logger.service';

@Injectable()
export class CashPaymentStrategy extends BasePaymentStrategy {
  constructor(
    paymentRepository: PaymentRepository,
    bookingsService: BookingsService,
    logger: LoggerService,
  ) {
    super(paymentRepository, bookingsService, logger, PaymentMethodEnum.CASH);
  }

  async handle(context: PaymentContext): Promise<PaymentResult> {
    this.logger.debug({
      message: 'Handling cash payment',
      bookingId: context.booking.id,
      correlationId: context.correlationId,
    });

    // Generate receipt number for cash payments
    const receiptNumber = `CASH-${Date.now().toString().slice(-6)}-${Math.floor(
      Math.random() * 1000,
    )
      .toString()
      .padStart(3, '0')}`;

    const payment = await this.createOrUpdatePaymentRecord(context, {
      receiptNumber,
      paymentProcessor: 'Manual',
      notes: 'Cash payment confirmed manually',
      status: 'completed', // Cash payments are immediately completed when confirmed
    });

    await this.processBookingPayment(context, payment);

    return {
      status: 'success',
      message: 'Cash payment recorded successfully',
      bookingId: context.booking.id,
      paymentId: payment.id,
      referenceNumber: receiptNumber,
    };
  }
}
